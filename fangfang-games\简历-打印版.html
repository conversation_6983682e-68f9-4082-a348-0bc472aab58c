<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>李子奇 - 个人简历</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #fff;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        .header-left {
            flex: 1;
        }
        .name {
            font-size: 28px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        .title {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }
        .contact-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
        }
        .photo-container {
            width: 120px;
            height: 160px;
            margin-left: 20px;
            border: 2px solid #2c5aa0;
            border-radius: 5px;
            overflow: hidden;
            background-color: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .photo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c5aa0;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .skill-category {
            margin-bottom: 10px;
        }
        .skill-category-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        .project {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #2c5aa0;
            background-color: #f8f9fa;
        }
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .project-name {
            font-weight: bold;
            font-size: 16px;
            color: #2c5aa0;
        }
        .project-time {
            color: #666;
            font-size: 14px;
        }
        .project-desc {
            margin-bottom: 10px;
            color: #555;
        }
        .responsibilities {
            margin-left: 0;
            padding-left: 0;
        }
        .responsibilities li {
            margin-bottom: 5px;
            list-style-type: none;
            position: relative;
            padding-left: 15px;
        }
        .responsibilities li:before {
            content: "•";
            color: #2c5aa0;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .education-item, .work-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .advantages {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }
        .advantage-item {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #2c5aa0;
        }
        .advantage-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        @page {
            margin: 0.5in;
            size: A4;
        }
        @media print {
            body { 
                margin: 0; 
                padding: 10px; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .container { 
                box-shadow: none; 
                padding: 20px; 
                page-break-inside: avoid;
            }
            .project {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <div class="name">李子奇</div>
                <div class="title">测试工程师</div>
                <div class="contact-info">
                    <div><strong>联系电话：</strong>15136355262</div>
                    <div><strong>出生年月：</strong>2000年9月</div>
                    <div><strong>电子邮箱：</strong><EMAIL></div>
                    <div><strong>政治面貌：</strong>共青团员</div>
                    <div><strong>毕业院校：</strong>郑州轻工业大学</div>
                    <div><strong>学历：</strong>本科</div>
                </div>
            </div>
            <div class="photo-container">
                <img src="李子奇.jpg" alt="李子奇个人照片">
            </div>
        </div>

        <div class="section">
            <div class="section-title">专业技能</div>
            <div class="skills-grid">
                <div class="skill-category">
                    <div class="skill-category-title">测试基础：</div>
                    <div>• 掌握完整的软件测试工作流程和常用测试方法</div>
                    <div>• 具备独立进行需求分析、编写测试用例、执行测试、编写测试报告的能力</div>
                    <div>• 熟练使用 XMind 进行测试点分析和业务逻辑梳理</div>
                </div>
                <div class="skill-category">
                    <div class="skill-category-title">测试工具：</div>
                    <div>• 缺陷管理：熟练使用 Mantis、Tapd 进行缺陷跟踪和管理</div>
                    <div>• 接口测试：熟练使用 Postman 进行接口功能和自动化测试</div>
                    <div>• 抓包分析：熟练使用 Fiddler、F12 开发者工具进行网络抓包和问题定位</div>
                    <div>• 移动测试：掌握 ADB 命令，能使用 Monkey 工具进行 App 稳定性测试</div>
                    <div>• 性能测试：掌握 JMeter 工具进行接口性能测试和压力测试</div>
                </div>
                <div class="skill-category">
                    <div class="skill-category-title">大数据技术能力：</div>
                    <div>• 数据库：熟练使用 MySQL 进行大数据查询优化和数据验证，掌握复杂SQL语句编写</div>
                    <div>• 大数据测试：具备ETL流程测试经验，熟悉数据采集、清洗、转换各环节的测试方法</div>
                    <div>• 可视化测试：熟练测试各类数据可视化组件，包括图表渲染、交互功能和性能优化</div>
                    <div>• 编程语言：熟练使用 Python 编写数据接口自动化测试脚本和数据验证工具</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">个人优势</div>
            <div class="advantages">
                <div class="advantage-item">
                    <div class="advantage-title">沟通协作：</div>
                    <div>具备良好的沟通能力，能与开发、产品、项目经理、客户等各方人员保持良好的协作关系</div>
                </div>
                <div class="advantage-item">
                    <div class="advantage-title">学习能力：</div>
                    <div>具有较强的学习能力和问题解决能力，善于发现问题、分析问题并总结经验</div>
                </div>
                <div class="advantage-item">
                    <div class="advantage-title">工作态度：</div>
                    <div>工作认真负责、积极主动，善于通过各种渠道快速获取有效信息</div>
                </div>
                <div class="advantage-item">
                    <div class="advantage-title">适应能力：</div>
                    <div>能够快速适应各种工作环境，具有较强的抗压能力</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">项目经历</div>

            <div class="project">
                <div class="project-header">
                    <div class="project-name">卡特彼勒产品竞品分析平台</div>
                    <div class="project-time">2025.02 - 至今</div>
                </div>
                <div class="project-desc">
                    基于海量媒体数据的工程机械行业竞品分析平台。通过爬虫技术从多个媒体渠道采集产品发布资讯和用户评论数据，运用数据挖掘技术进行情感分析和竞品对比，通过交互式图表和仪表盘为卡特彼勒提供实时的市场竞争态势分析和产品优劣势洞察。
                </div>
                <ul class="responsibilities">
                    <li>负责大数据ETL流程测试，验证数据采集、清洗、转换各环节的准确性</li>
                    <li>测试可视化图表的数据准确性和交互功能，包括动态仪表盘、趋势分析图等</li>
                    <li>开发Python自动化脚本，批量验证数据接口和可视化组件的性能</li>
                    <li>测试实时数据流处理功能，确保大数据平台的稳定性和响应速度</li>
                    <li>验证情感分析和竞品评分算法的准确性和数据处理逻辑</li>
                </ul>
            </div>

            <div class="project">
                <div class="project-header">
                    <div class="project-name">Ecoflow电商数据分析平台</div>
                    <div class="project-time">2024.03 - 2024.12</div>
                </div>
                <div class="project-desc">
                    面向新能源储能设备的全渠道电商数据分析平台。整合亚马逊、京东、天猫等主流电商平台的海量评论数据，通过NLP技术进行情感分析和关键词提取，构建多维度数据分析体系，以动态大屏和交互式报表形式展示产品健康度、用户满意度和市场表现趋势。
                </div>
                <ul class="responsibilities">
                    <li>测试多源电商数据采集接口，验证数据同步的完整性和实时性</li>
                    <li>验证大数据处理逻辑，包括文本挖掘、情感分析和评分计算功能</li>
                    <li>测试可视化大屏的数据展示效果，确保图表渲染性能和用户体验</li>
                    <li>进行大数据量压力测试，验证系统在高并发场景下的稳定性</li>
                    <li>协助优化数据仓库查询性能，提升可视化报表的加载速度</li>
                </ul>
            </div>

            <div class="project">
                <div class="project-header">
                    <div class="project-name">深圳金融舆情风险防范平台</div>
                    <div class="project-time">2023.01 - 2023.12</div>
                </div>
                <div class="project-desc">
                    面向深圳市金融监管部门的智能风险防控平台。基于海量金融舆情数据和交易数据，运用数据分析技术进行实时风险识别和预警。通过数据分析算法构建风险评估模型，以态势感知大屏、预警地图和动态图表等形式，为监管部门提供全方位的金融风险态势展示和决策支持。
                </div>
                <ul class="responsibilities">
                    <li>测试大数据实时流处理功能，验证金融数据采集和风险计算的准确性</li>
                    <li>验证风险评估算法和数据分析模型的预测准确率和响应时间</li>
                    <li>测试可视化态势感知大屏，包括实时风险地图、趋势图表和预警面板</li>
                    <li>进行高并发压力测试，确保系统在大数据量场景下的稳定运行</li>
                    <li>测试多维度数据钻取和交互式分析功能的性能和准确性</li>
                    <li>验证跨部门数据共享接口和协同处置工作流的完整性</li>
                </ul>
            </div>

            <div class="project">
                <div class="project-header">
                    <div class="project-name">新华网金融舆情数字平台</div>
                    <div class="project-time">2022.07 - 2023.02</div>
                </div>
                <div class="project-desc">
                    新华网旗下的金融行业舆情监测和分析平台。整合全网金融资讯数据，通过爬虫技术采集海量新闻、评论和社交媒体数据，运用NLP和情感分析技术构建金融健康指数体系。以交互式报表、动态仪表盘和定制化分析报告的形式，为金融机构提供行业趋势洞察和风险预警服务。
                </div>
                <ul class="responsibilities">
                    <li>测试大数据ETL管道，验证多源数据采集、清洗和标注流程的准确性</li>
                    <li>验证金融健康指数计算逻辑和情感分析功能的计算精度</li>
                    <li>测试可视化组件库，包括动态图表、交互式仪表盘和数据钻取功能</li>
                    <li>使用JMeter进行大数据平台性能测试，优化查询响应速度</li>
                    <li>测试定制化报告生成功能，验证数据导出和格式转换的准确性</li>
                    <li>进行多浏览器兼容性测试，确保可视化效果的一致性</li>
                    <li>协助客户进行UAT测试，收集反馈并推动产品迭代优化</li>
                </ul>
            </div>

            <div class="project">
                <div class="project-header">
                    <div class="project-name">R7-R15项目管理平台</div>
                    <div class="project-time">2022.07 - 至今</div>
                </div>
                <div class="project-desc">
                    公司核心的数据中台和项目管理系统。集成多个项目的数据采集、处理和分析流程，通过统一的管理界面实现数据源配置、ETL规则管理、数据质量监控和项目进度跟踪。为客服团队和项目管理人员提供直观的数据运营大屏和业务监控仪表盘。
                </div>
                <ul class="responsibilities">
                    <li>测试大数据采集规则配置功能，验证多源数据接入的稳定性</li>
                    <li>验证数据质量监控算法，确保数据准确性和完整性检测</li>
                    <li>测试可视化运营大屏，包括实时数据流监控和项目状态展示</li>
                    <li>进行数据管道压力测试，验证大数据处理的性能和容错能力</li>
                    <li>测试权限管理和多租户隔离功能，确保数据安全性</li>
                    <li>协调跨项目数据共享测试，验证平台的扩展性和兼容性</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <div class="section-title">教育背景</div>
            <div class="education-item">
                <div>
                    <strong>郑州轻工业大学</strong> - 计算机科学与技术（本科）<br>
                    <small>主修课程：Java、JavaScript、MySQL、HTML、JavaWeb、数据结构、算法设计等</small>
                </div>
                <div>2023.04 - 2025.07</div>
            </div>
            <div class="education-item">
                <div><strong>河南职业技术学院</strong> - 软件技术（大专）</div>
                <div>2019.09 - 2021.07</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">工作经历</div>
            <div class="work-item">
                <div>
                    <strong>北京新智科技发展有限公司</strong> - 测试工程师<br>
                    <small>• 负责多个大型项目的测试工作，包括金融舆情平台、数据分析平台等<br>
                    • 参与完整的软件测试生命周期，从需求分析到测试报告编写<br>
                    • 使用多种测试工具和技术，提高测试效率和质量<br>
                    • 与跨部门团队协作，确保项目按时高质量交付</small>
                </div>
                <div>2022.07 - 至今</div>
            </div>
            <div class="work-item">
                <div>
                    <strong>北京视创动力科技有限公司</strong> - 测试工程师<br>
                    <small>• 参与软件产品的功能测试和兼容性测试<br>
                    • 学习和掌握基础的测试理论和实践技能<br>
                    • 积累了丰富的测试经验和团队协作能力</small>
                </div>
                <div>2021.07 - 2022.06</div>
            </div>
        </div>
    </div>
</body>
</html>
