李子奇 - 测试工程师

═══════════════════════════════════════════════════════════════════════════════

                                                                    ┌─────────────┐
基本信息                                                            │             │
───────────────────────────────────────────────────────────────────│   个人照片   │
姓    名：李子奇                    出生年月：2000年9月              │             │
联系电话：15136355262               电子邮箱：<EMAIL>   │  请在此处   │
求职意向：测试工程师                政治面貌：共青团员              │  粘贴照片   │
毕业院校：郑州轻工业大学            学    历：本科                  │             │
                                                                    └─────────────┘

专业技能
───────────────────────────────────────────────────────────────────────────────
测试基础：
• 掌握完整的软件测试工作流程和常用测试方法
• 具备独立进行需求分析、编写测试用例、执行测试、编写测试报告的能力
• 熟练使用 XMind 进行测试点分析和业务逻辑梳理

测试工具：
• 缺陷管理：熟练使用 Mantis、Tapd 进行缺陷跟踪和管理
• 接口测试：熟练使用 Postman 进行接口功能和自动化测试
• 抓包分析：熟练使用 Fiddler、F12 开发者工具进行网络抓包和问题定位
• 移动测试：掌握 ADB 命令，能使用 Monkey 工具进行 App 稳定性测试
• 性能测试：掌握 JMeter 工具进行接口性能测试和压力测试

大数据技术能力：
• 数据库：熟练使用 MySQL 进行大数据查询优化和数据验证，掌握复杂SQL语句编写
• 大数据测试：具备ETL流程测试经验，熟悉数据采集、清洗、转换各环节的测试方法
• 可视化测试：熟练测试各类数据可视化组件，包括图表渲染、交互功能和性能优化
• 编程语言：熟练使用 Python 编写数据接口自动化测试脚本和数据验证工具
• 算法验证：具备机器学习算法测试经验，能够验证数据模型的准确性和性能
• 系统运维：掌握 Linux 系统管理，熟悉大数据平台的部署和监控

个人优势
───────────────────────────────────────────────────────────────────────────────
• 沟通协作：具备良好的沟通能力，能与开发、产品、项目经理、客户等各方人员保持良好的协作关系
• 学习能力：具有较强的学习能力和问题解决能力，善于发现问题、分析问题并总结经验
• 工作态度：工作认真负责、积极主动，善于通过各种渠道快速获取有效信息
• 适应能力：能够快速适应各种工作环境，具有较强的抗压能力


项目经历
───────────────────────────────────────────────────────────────────────────────

【卡特彼勒产品竞品分析大数据可视化平台】                        2025.02 - 至今
项目描述：
大数据可视化项目 - 基于海量媒体数据的工程机械行业竞品分析平台。通过大数据爬虫技术从多个媒体渠道采集产品发布资讯和用户评论数据，运用数据挖掘和机器学习算法进行情感分析和竞品对比，通过交互式可视化图表为卡特彼勒提供实时的市场竞争态势分析和产品优劣势洞察。

主要职责：
• 负责大数据ETL流程测试，验证数据采集、清洗、转换各环节的准确性
• 测试可视化图表的数据准确性和交互功能，包括动态仪表盘、趋势分析图等
• 开发Python自动化脚本，批量验证数据接口和可视化组件的性能
• 测试实时数据流处理功能，确保大数据平台的稳定性和响应速度
• 验证机器学习算法的准确性，包括情感分析模型和竞品评分算法

【深圳金融舆情稳定态势感知与风险防范平台】                      2023.01 - 2023.12
项目描述：
政府金融监管平台，主要功能包括金融事件风险评估、涉事主体识别、风险预警和相关部门协同处置，
为金融风险防范提供技术支撑。

主要职责：
• 根据需求规格说明书进行深入的需求分析
• 使用 XMind 工具梳理复杂业务流程，提取关键测试点
• 设计并编写全面的测试用例，覆盖各种业务场景
• 执行测试用例，使用 Tapd 进行缺陷管理和跟踪
• 进行多浏览器兼容性测试（IE、Chrome、Edge）
• 负责缺陷返测和测试文档整理，编写测试总结报告

【新华金融舆情数字平台】（新华网）                             2022.07 - 2023.02
项目描述：
定制化金融舆情监测平台，通过数据爬取、标注和可视化展示，为客户提供金融行业健康指数、
子行业分析和企业健康度评估，支持报告生成和数据对比等功能。

主要职责：
• 参与前期需求评审，深入理解业务流程和客户需求
• 使用 XMind 分析测试点，编写高质量测试用例
• 参与组内用例评审，持续改进测试用例质量
• 使用 MySQL 进行数据验证，辅助测试执行
• 使用 JMeter 进行性能测试，协助开发进行系统优化
• 执行多浏览器兼容性测试，确保系统稳定性
• 跟踪客户新需求，确保开发进度和测试质量
• 使用 Tapd 进行缺陷管理，编写测试报告

【R7-R15项目管理平台】                                        2022.07 - 至今
项目描述：
公司核心项目管理平台，为客服团队提供数据采集、达标规则配置和数据编审功能，
是公司所有项目的数据中枢系统。

主要职责：
• 根据客服需求编写和维护测试用例
• 负责新功能测试和回归测试，确保系统稳定性
• 使用 Tapd 进行缺陷跟踪和管理
• 定期测试主流媒体数据抓取功能，确保数据采集正常
• 协调项目上线和客服通知工作

【Ecoflow产品评论数据分析平台】                               2024.03 - 2024.12
项目描述：
为Ecoflow公司开发的产品评论数据分析平台，从亚马逊、京东、天猫等主流电商平台抓取产品评论数据，
通过可视化展示产品健康度和推荐值。

主要职责：
• 进行需求分析和测试用例编写
• 执行项目测试，使用 Tapd 记录和管理缺陷
• 编写每日测试报告，参加周例会汇报项目进度
• 验证算法准确性和数据量统计，与开发团队协作解决问题

教育背景
───────────────────────────────────────────────────────────────────────────────
2023.04 - 2025.07    郑州轻工业大学                    计算机科学与技术（本科）
2019.09 - 2021.07    河南职业技术学院                  软件技术（大专）

主修课程：Java、JavaScript、MySQL、HTML、JavaWeb、数据结构、算法设计等

工作经历
───────────────────────────────────────────────────────────────────────────────
2022.07 - 至今       北京新智科技发展有限公司          测试工程师
• 负责多个大型项目的测试工作，包括金融舆情平台、数据分析平台等
• 参与完整的软件测试生命周期，从需求分析到测试报告编写
• 使用多种测试工具和技术，提高测试效率和质量
• 与跨部门团队协作，确保项目按时高质量交付

2021.07 - 2022.06    北京视创动力科技有限公司          测试工程师
• 参与软件产品的功能测试和兼容性测试
• 学习和掌握基础的测试理论和实践技能
• 积累了丰富的测试经验和团队协作能力

═══════════════════════════════════════════════════════════════════════════════